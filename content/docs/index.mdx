---
title: 项目简介
icon: House
description: HiveChat 是专为中小团队设计的 AI 聊天应用，支持多用户模式，支持权限管理、MCP，以及 Deepseek、Open AI、Claude、Gemini 多种模型接入。
---

## 功能概览
管理员一人配置，全团队轻松使用各种 AI 模型。

![image](/images/index/01.png)

* 支持配置邮箱登录或企业微信、钉钉、飞书登录
* 支持分组管理用户
    * 针对分组用户设置不同可使用的模型
    * 针对分组用户可分别设置每月 Token 限额
* 支持配置 MCP 服务器
* DeepSeek 思维链展示
* LaTeX 和 Markdown 渲染
* 图像理解
* AI 智能体
* 云端数据存储
* 支持的大模型服务商：
    * Open AI
    * Claude
    * Gemini
    * DeepSeek
    * Moonshot(月之暗面)
    * 火山方舟（豆包）
    * 阿里百炼（千问）
    * 百度千帆
    * 腾讯混元
    * 智谱
    * Open Router
    * Grok
    * Ollama
    * 硅基流动
 * 同时支持自定义添加任意 Open AI 风格的服务商

### 普通用户端
登录账号，即可对话。
![image](/images/index/01.png)

MCP 使用

![image](/images/index/02.png)

### 管理后台

* 管理员配置 AI 大模型服务商
* 可手动添加用户，也可开启或关闭账号注册，适用于公司/学校/组织等小型团队
* 查看和管理全部用户

![image](/images/index/03.png)

用户管理，可以为用户设置分组，针对不同分组设置可见模型和 Token 限额
<img src="/images/index/04.png" />
<img src="/images/index/05.png" />
邮箱以及第三方登录
<img src="/images/index/06.png" />
MCP 配置
<img src="/images/index/07.png" />


## 在线演示

注：以下为演示站，数据随时会被清空

* 用户端：https://chat.yotuku.cn/
    * 可自行注册账号体验
* 管理员端：https://hivechat-demo.vercel.app/
    * Email: <EMAIL>
    * Password: helloHivechat

## 技术栈

* Next.js
* Tailwindcss
* Auth.js
* PostgreSQL
* Drizzle ORM
* Ant Design