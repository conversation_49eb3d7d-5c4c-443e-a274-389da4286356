{"name": "hivechat-doc", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev --turbo", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"fumadocs-core": "15.2.15", "fumadocs-mdx": "11.6.2", "fumadocs-ui": "15.2.15", "lucide-react": "^0.509.0", "next": "15.3.1", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.5", "@types/mdx": "^2.0.13", "@types/node": "22.15.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "typescript": "^5.8.3"}}