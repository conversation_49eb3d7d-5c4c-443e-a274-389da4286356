@import 'tailwindcss';
@import 'fumadocs-ui/css/neutral.css';
@import 'fumadocs-ui/css/preset.css';

/* 平滑滚动 */
html {
  scroll-behavior: smooth;
}

/* 移动端菜单动画效果 */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 菜单按钮旋转效果 */
#mobile-menu-toggle:checked ~ nav label svg {
  transform: rotate(90deg);
}

nav label svg {
  transition: transform 0.3s ease;
}

/* 移动端菜单显示时的动画 */
#mobile-menu-toggle:checked ~ nav .peer-checked\:block {
  animation: slideDown 0.3s ease-out;
}
