import type { BaseLayoutProps } from 'fumadocs-ui/layouts/shared';
import Image from "next/image";
/**
 * Shared layout configurations
 *
 * you can customise layouts individually from:
 * Home Layout: app/(home)/layout.tsx
 * Docs Layout: app/docs/layout.tsx
 */
export const baseOptions: BaseLayoutProps = {
  nav: {
    title: (
      <>
        <Image src="/icon.png" alt="HiveChat" width={24} height={24} />
        HiveChat
      </>
    ),
  },

  githubUrl: 'https://github.com/HiveNexus/HiveChat',

  // links: [
  //   {
  //     text: 'Documentation1',
  //     url: '/docs',
  //     active: 'nested-url',
  //   },
  // ],
};
