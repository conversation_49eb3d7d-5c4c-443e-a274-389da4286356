import Link from 'next/link';
import Image from 'next/image';
import { <PERSON>Right, Github, ExternalLink, Users, Shield, Zap, Globe, Database, Cpu, MessageSquare, Settings, BarChart3, Lock, Smartphone, Monitor } from 'lucide-react';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Hero Section */}
      <section className="relative px-6 lg:px-8 pt-20 pb-32">
        <div className="mx-auto max-w-7xl">
          <div className="text-center">
            <div className="flex justify-center mb-8">
              <Image src="/icon.png" alt="HiveChat" width={80} height={80} className="rounded-2xl shadow-lg" />
            </div>
            <h1 className="text-5xl font-bold tracking-tight text-gray-900 sm:text-7xl">
              <span className="text-blue-600">HiveChat</span>
            </h1>
            <p className="mt-6 text-xl leading-8 text-gray-600 max-w-3xl mx-auto">
              专为中小团队设计的 AI 聊天应用，支持多用户模式、权限管理、MCP 协议，以及 15+ 种主流 AI 模型接入
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="https://chat.yotuku.cn/"
                className="rounded-lg bg-blue-600 px-6 py-3 text-lg font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-all duration-200 flex items-center gap-2"
              >
                立即体验 <ExternalLink className="w-5 h-5" />
              </Link>
              <Link
                href="/docs"
                className="rounded-lg border border-gray-300 px-6 py-3 text-lg font-semibold text-gray-900 hover:bg-gray-50 transition-all duration-200 flex items-center gap-2"
              >
                查看文档 <ArrowRight className="w-5 h-5" />
              </Link>
            </div>
            <div className="mt-8 flex items-center justify-center gap-x-8 text-sm text-gray-500">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                开源免费
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                私有部署
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                企业级安全
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Core Features */}
      <section className="py-24 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              核心功能特性
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              管理员一人配置，全团队轻松使用各种 AI 模型
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-16 lg:max-w-none lg:grid-cols-4">
              <div className="flex flex-col items-center text-center">
                <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-lg bg-blue-600">
                  <Users className="h-8 w-8 text-white" />
                </div>
                <dt className="text-lg font-semibold leading-7 text-gray-900">多用户管理</dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">
                  支持分组管理用户，针对不同分组设置可用模型和 Token 限额
                </dd>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-lg bg-green-600">
                  <Shield className="h-8 w-8 text-white" />
                </div>
                <dt className="text-lg font-semibold leading-7 text-gray-900">权限控制</dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">
                  细粒度权限管理，支持邮箱、企业微信、钉钉、飞书等多种登录方式
                </dd>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-lg bg-purple-600">
                  <Zap className="h-8 w-8 text-white" />
                </div>
                <dt className="text-lg font-semibold leading-7 text-gray-900">MCP 协议</dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">
                  支持 Model Context Protocol，轻松扩展 AI 能力和工具集成
                </dd>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="mb-6 flex h-16 w-16 items-center justify-center rounded-lg bg-orange-600">
                  <Globe className="h-8 w-8 text-white" />
                </div>
                <dt className="text-lg font-semibold leading-7 text-gray-900">多模型接入</dt>
                <dd className="mt-2 text-base leading-7 text-gray-600">
                  支持 15+ 种主流 AI 服务商，一键切换不同模型
                </dd>
              </div>
            </dl>
          </div>
        </div>
      </section>

      {/* AI Models */}
      <section className="py-24 bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              支持的 AI 模型
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              集成主流 AI 服务商，满足不同场景需求
            </p>
          </div>
          <div className="mx-auto mt-16 grid max-w-lg grid-cols-2 items-center gap-x-8 gap-y-12 sm:max-w-xl sm:grid-cols-3 sm:gap-x-10 lg:mx-0 lg:max-w-none lg:grid-cols-6">
            {[
              'OpenAI', 'Claude', 'Gemini', 'DeepSeek', 'Moonshot', '豆包',
              '千问', '百度千帆', '腾讯混元', '智谱', 'Grok', 'Ollama'
            ].map((model) => (
              <div key={model} className="col-span-1 flex justify-center">
                <div className="bg-white rounded-lg px-4 py-3 shadow-sm border border-gray-200 text-center min-w-[100px]">
                  <span className="text-sm font-medium text-gray-900">{model}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-24 bg-white">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              丰富的功能特性
            </h2>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-8 lg:max-w-none lg:grid-cols-3">
              {[
                { icon: MessageSquare, title: 'DeepSeek 思维链', desc: '可视化展示 AI 思考过程' },
                { icon: Database, title: 'LaTeX & Markdown', desc: '完美渲染数学公式和文档' },
                { icon: Monitor, title: '图像理解', desc: '支持图片上传和多模态对话' },
                { icon: Cpu, title: 'AI 智能体', desc: '自定义 AI 助手和工作流' },
                { icon: Lock, title: '云端存储', desc: '安全可靠的数据存储方案' },
                { icon: BarChart3, title: 'Token 管理', desc: '精确的使用量统计和限额控制' }
              ].map((feature) => (
                <div key={feature.title} className="flex flex-col">
                  <div className="flex items-center gap-x-3">
                    <feature.icon className="h-6 w-6 text-blue-600" />
                    <dt className="text-lg font-semibold leading-7 text-gray-900">{feature.title}</dt>
                  </div>
                  <dd className="mt-2 text-base leading-7 text-gray-600">{feature.desc}</dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </section>

      {/* Deployment Options */}
      <section className="py-24 bg-gray-50">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              灵活的部署方式
            </h2>
            <p className="mt-4 text-lg leading-8 text-gray-600">
              支持多种部署方案，满足不同团队需求
            </p>
          </div>
          <div className="mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-none">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
                <div className="flex items-center gap-x-3 mb-4">
                  <Smartphone className="h-8 w-8 text-blue-600" />
                  <h3 className="text-xl font-semibold text-gray-900">本地部署</h3>
                </div>
                <p className="text-gray-600 mb-6">完全私有化部署，数据安全可控</p>
                <Link href="/docs/install/local" className="text-blue-600 hover:text-blue-500 font-medium">
                  查看教程 →
                </Link>
              </div>
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
                <div className="flex items-center gap-x-3 mb-4">
                  <Database className="h-8 w-8 text-green-600" />
                  <h3 className="text-xl font-semibold text-gray-900">Docker 部署</h3>
                </div>
                <p className="text-gray-600 mb-6">容器化部署，快速启动和扩展</p>
                <Link href="/docs" className="text-blue-600 hover:text-blue-500 font-medium">
                  查看教程 →
                </Link>
              </div>
              <div className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200">
                <div className="flex items-center gap-x-3 mb-4">
                  <Globe className="h-8 w-8 text-purple-600" />
                  <h3 className="text-xl font-semibold text-gray-900">Vercel 部署</h3>
                </div>
                <p className="text-gray-600 mb-6">一键部署到云端，零运维成本</p>
                <Link href="/docs/install/vercel" className="text-blue-600 hover:text-blue-500 font-medium">
                  查看教程 →
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-blue-600">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
              立即开始使用 HiveChat
            </h2>
            <p className="mt-6 text-lg leading-8 text-blue-100">
              开源免费，支持私有部署，为您的团队提供安全可靠的 AI 聊天解决方案
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Link
                href="https://chat.yotuku.cn/"
                className="rounded-lg bg-white px-6 py-3 text-lg font-semibold text-blue-600 shadow-sm hover:bg-gray-50 transition-all duration-200 flex items-center gap-2"
              >
                在线演示 <ExternalLink className="w-5 h-5" />
              </Link>
              <Link
                href="https://github.com/HiveNexus/HiveChat"
                className="rounded-lg border border-white px-6 py-3 text-lg font-semibold text-white hover:bg-blue-500 transition-all duration-200 flex items-center gap-2"
              >
                <Github className="w-5 h-5" />
                GitHub
              </Link>
            </div>
            <div className="mt-8 text-sm text-blue-100">
              <p>演示站点：用户端和管理端均可体验</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 py-12">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="flex flex-col items-center justify-center">
            <div className="flex items-center gap-x-3 mb-4">
              <Image src="/icon.png" alt="HiveChat" width={32} height={32} className="rounded-lg" />
              <span className="text-xl font-bold text-white">HiveChat</span>
            </div>
            <p className="text-gray-400 text-center max-w-md">
              专为中小团队设计的 AI 聊天应用，让 AI 协作更简单
            </p>
            <div className="mt-6 flex items-center gap-x-6">
              <Link href="/docs" className="text-gray-400 hover:text-white transition-colors">
                文档
              </Link>
              <Link href="https://github.com/HiveNexus/HiveChat" className="text-gray-400 hover:text-white transition-colors">
                GitHub
              </Link>
              <Link href="https://chat.yotuku.cn/" className="text-gray-400 hover:text-white transition-colors">
                演示
              </Link>
            </div>
            <div className="mt-8 border-t border-gray-800 pt-8 text-center">
              <p className="text-gray-400 text-sm">
                © 2024 HiveChat. 基于 MIT 协议开源
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
